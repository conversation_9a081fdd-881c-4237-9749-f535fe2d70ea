@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 - 简化版本 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 修复水合不匹配：明确设置 overscroll 行为 */
  overscroll-behavior-x: none;
}

/* 矩阵专用样式 */
.matrix-cell {
  transition: all 0.1s ease;
  box-sizing: border-box;
  user-select: none;
  cursor: pointer;
}

.matrix-cell:hover {
  transform: scale(1.05);
  z-index: 10;
}

.matrix-cell.selected {
  box-shadow: 0 0 0 2px #3b82f6;
}

.matrix-cell.coordinate-mode {
  font-family: 'Monaco', '<PERSON><PERSON>', monospace;
  font-size: 10px;
}

.matrix-cell.color-mode {
  font-weight: bold;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
}

.matrix-cell.level-mode {
  font-family: 'Arial', sans-serif;
  font-weight: bold;
}

.matrix-cell.word-mode {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  line-height: 1.2;
}

/* 性能优化 */
.matrix-container {
  will-change: transform;
  contain: layout style paint;
  /* 响应式支持 */
  box-sizing: border-box;
  overflow: hidden;
}

/* 响应式矩阵容器 */
.matrix-container.responsive {
  width: 100% !important;
  height: 100% !important;
  max-width: 100vmin;
  max-height: 100vmin;
  aspect-ratio: 1;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .matrix-container {
    max-width: 95vw;
    max-height: 95vh;
  }

  .matrix-cell {
    /* 移动端更大的触摸目标 */
    min-width: 12px;
    min-height: 12px;
  }

  .matrix-cell:hover {
    /* 移动端减少hover效果 */
    transform: none;
  }
}

/* 小屏幕适配 */
@media (max-width: 480px) {
  .matrix-container {
    max-width: 90vw;
    max-height: 90vh;
  }
}