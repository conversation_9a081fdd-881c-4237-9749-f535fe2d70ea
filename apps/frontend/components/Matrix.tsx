/**
 * 矩阵主组件
 * 🎯 核心价值：纯渲染组件，数据驱动视图，零业务逻辑
 * 📦 功能范围：矩阵渲染、交互事件传递、性能优化
 * 🔄 架构设计：完全无状态组件，所有逻辑通过props注入
 */

'use client';

import { matrixCore } from '@/core/matrix/MatrixCore';
import { useMatrixConfig, useMatrixData, useMatrixStore } from '@/core/matrix/MatrixStore';
import type {
  BusinessMode,
  Coordinate,
  MatrixConfig,
  ResponsiveConfig,
} from '@/core/matrix/MatrixTypes';
import { MATRIX_SIZE } from '@/core/matrix/MatrixTypes';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';

import { createInteractionEvent } from '@/core/matrix/MatrixCore';

// ===== 组件属性 =====

interface MatrixProps {
  /** 自定义配置覆盖 */
  configOverride?: Partial<MatrixConfig>;

  /** 响应式配置 */
  responsiveConfig?: Partial<ResponsiveConfig>;

  /** 容器样式 */
  className?: string;
  style?: React.CSSProperties;

  /** 交互事件回调 */
  onCellClick?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellDoubleClick?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellHover?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellFocus?: (coordinate: Coordinate, event: React.FocusEvent) => void;
  onModeChange?: (mode: BusinessMode) => void;

  /** 性能配置 */
  enablePerformanceMonitoring?: boolean;
  debugMode?: boolean;
}

// ===== 主组件 =====

const MatrixComponent: React.FC<MatrixProps> = ({
  configOverride,
  responsiveConfig,
  className = '',
  style,
  onCellClick,
  onCellDoubleClick,
  onCellHover,
  onCellFocus,
  onModeChange,
  enablePerformanceMonitoring = false,
  debugMode = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const isInitialized = useRef(false);
  const [isClient, setIsClient] = useState(false);

  // 响应式状态
  const [containerSize, setContainerSize] = useState({ width: 800, height: 600 });
  const [cellSize, setCellSize] = useState(20);

  // 计算响应式尺寸
  const calculateResponsiveSizes = useCallback(() => {
    if (!containerRef.current || !responsiveConfig?.enabled) {
      return { cellSize: 20, matrixSize: MATRIX_SIZE * 22 };
    }

    const rect = containerRef.current.getBoundingClientRect();
    const availableSize = Math.min(rect.width, rect.height);
    const calculatedCellSize = Math.max(8, Math.min(32, availableSize / MATRIX_SIZE));
    const gap = calculatedCellSize * 0.1;
    const matrixSize = MATRIX_SIZE * calculatedCellSize + (MATRIX_SIZE - 1) * gap;

    return {
      cellSize: calculatedCellSize,
      gap,
      matrixSize: Math.min(matrixSize, availableSize)
    };
  }, [responsiveConfig, containerRef]);

  // 监听容器尺寸变化
  useEffect(() => {
    if (!containerRef.current || !responsiveConfig?.enabled) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setContainerSize({ width, height });

        const { cellSize: newCellSize } = calculateResponsiveSizes();
        setCellSize(newCellSize);
      }
    });

    resizeObserver.observe(containerRef.current);

    // 初始计算
    const { cellSize: initialCellSize } = calculateResponsiveSizes();
    setCellSize(initialCellSize);

    return () => resizeObserver.disconnect();
  }, [calculateResponsiveSizes, responsiveConfig]);

  // 获取状态
  const matrixData = useMatrixData();
  const matrixConfig = useMatrixConfig();
  const {
    initializeMatrix,
    selectCell,
    hoverCell,
    focusCell,
    setMode,
    getCellRenderData,
  } = useMatrixStore();

  // 合并配置
  const finalConfig = { ...matrixConfig, ...configOverride };

  // 确保客户端渲染一致性
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 初始化
  useEffect(() => {
    if (!isInitialized.current && containerRef.current && isClient) {
      // 初始化矩阵数据
      if (matrixData.cells.size === 0) {
        initializeMatrix();
      }
      isInitialized.current = true;
    }
  }, [initializeMatrix, matrixData.cells.size, isClient]);

  // 渲染矩阵单元格
  const renderMatrixCells = useCallback(() => {
    const cells = [];
    const gap = cellSize * 0.1;

    for (let y = 0; y < MATRIX_SIZE; y++) {
      for (let x = 0; x < MATRIX_SIZE; x++) {
        const cellRenderData = getCellRenderData(x, y);
        const key = `${x},${y}`;

        cells.push(
          <div
            key={key}
            data-x={x}
            data-y={y}
            className={cellRenderData?.className || 'matrix-cell'}
            style={{
              position: 'absolute',
              left: `${x * (cellSize + gap)}px`,
              top: `${y * (cellSize + gap)}px`,
              width: `${cellSize}px`,
              height: `${cellSize}px`,
              border: '1px solid #e5e7eb',
              backgroundColor: cellRenderData?.style?.backgroundColor || '#ffffff',
              color: cellRenderData?.style?.color || '#000000',
              fontSize: `${Math.max(8, cellSize * 0.4)}px`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              userSelect: 'none',
              ...cellRenderData?.style,
            }}
          >
            {cellRenderData?.content || ''}
          </div>
        );
      }
    }
    return cells;
  }, [getCellRenderData, cellSize]);

  // 处理单元格点击
  const handleCellClick = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);

    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };

      // 更新状态
      selectCell(x, y, event.ctrlKey || event.metaKey);

      // 创建交互事件
      const interactionEvent = createInteractionEvent('click', coordinate, {
        ctrl: event.ctrlKey,
        shift: event.shiftKey,
        alt: event.altKey,
      });

      // 处理业务逻辑
      const cell = matrixData.cells.get(`${x},${y}`);
      if (cell) {
        matrixCore.handleInteraction(interactionEvent, cell, finalConfig);
      }

      // 调用外部回调
      onCellClick?.(coordinate, event);
    }
  }, [matrixData.cells, finalConfig, selectCell, onCellClick]);

  // 处理双击
  const handleCellDoubleClick = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);

    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };
      onCellDoubleClick?.(coordinate, event);
    }
  }, [onCellDoubleClick]);

  // 处理悬停
  const handleCellMouseEnter = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);

    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };
      hoverCell(x, y);
      onCellHover?.(coordinate, event);
    }
  }, [hoverCell, onCellHover]);

  // 处理焦点
  const handleCellFocus = useCallback((event: React.FocusEvent) => {
    const target = event.target as HTMLElement;
    const x = parseInt(target.dataset.x || '0', 10);
    const y = parseInt(target.dataset.y || '0', 10);

    if (!isNaN(x) && !isNaN(y)) {
      const coordinate = { x, y };
      focusCell(x, y);
      onCellFocus?.(coordinate, event);
    }
  }, [focusCell, onCellFocus]);

  // 处理键盘事件
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    // 模式切换快捷键
    switch (event.key) {
      case '1':
        if (event.ctrlKey) {
          setMode('coordinate');
          onModeChange?.('coordinate');
          event.preventDefault();
        }
        break;
      case '2':
        if (event.ctrlKey) {
          setMode('color');
          onModeChange?.('color');
          event.preventDefault();
        }
        break;
      case '3':
        if (event.ctrlKey) {
          setMode('level');
          onModeChange?.('level');
          event.preventDefault();
        }
        break;
      case '4':
        if (event.ctrlKey) {
          setMode('word');
          onModeChange?.('word');
          event.preventDefault();
        }
        break;
    }
  }, [setMode, onModeChange]);

  // 容器样式
  const containerStyle: React.CSSProperties = {
    position: 'relative',
    width: '100%',
    height: '100%',
    overflow: 'hidden',
    userSelect: 'none',
    ...style,
  };

  // 调试信息
  const debugInfo = debugMode && isClient ? (
    <div className="absolute top-2 left-2 bg-black bg-opacity-75 text-white p-2 text-xs rounded z-10">
      <div>Mode: {finalConfig.mode}</div>
      <div>Cells: {matrixData.cells.size}</div>
      <div>Selected: {matrixData.selectedCells.size}</div>
      <div>Hovered: {matrixData.hoveredCell || 'none'}</div>

    </div>
  ) : null;

  // 在客户端渲染完成前显示占位符
  if (!isClient) {
    return (
      <div
        className={`matrix-container ${className}`}
        style={{ ...containerStyle, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
      >
        <div className="text-gray-500">矩阵加载中...</div>
      </div>
    );
  }

  // 计算容器样式
  const gap = cellSize * 0.1;
  const matrixSize = MATRIX_SIZE * cellSize + (MATRIX_SIZE - 1) * gap;

  return (
    <div
      ref={containerRef}
      className={`matrix-container ${className}`}
      style={{
        ...style,
        position: 'relative',
        width: responsiveConfig?.enabled ? `${matrixSize}px` : `${MATRIX_SIZE * 22}px`,
        height: responsiveConfig?.enabled ? `${matrixSize}px` : `${MATRIX_SIZE * 22}px`,
        maxWidth: '100%',
        maxHeight: '100%',
        aspectRatio: '1',
      }}
      onClick={handleCellClick}
      onDoubleClick={handleCellDoubleClick}
      onMouseEnter={handleCellMouseEnter}
      onFocus={handleCellFocus}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="grid"
      aria-label="矩阵网格"
      aria-rowcount={33}
      aria-colcount={33}
    >
      {renderMatrixCells()}
      {debugInfo}
    </div>
  );
};

// ===== 性能优化 =====

const Matrix = memo(MatrixComponent, (prevProps, nextProps) => {
  // 自定义比较函数，避免不必要的重渲染
  return (
    prevProps.configOverride === nextProps.configOverride &&
    prevProps.className === nextProps.className &&
    prevProps.debugMode === nextProps.debugMode &&
    prevProps.enablePerformanceMonitoring === nextProps.enablePerformanceMonitoring
  );
});

Matrix.displayName = 'Matrix';

export default Matrix;
