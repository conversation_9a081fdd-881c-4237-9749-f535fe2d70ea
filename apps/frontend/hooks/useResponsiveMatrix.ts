/**
 * 响应式矩阵Hook
 * 🎯 核心价值：自适应矩阵尺寸，保持正方形比例，支持移动端
 * 📦 功能范围：容器监听、尺寸计算、视口管理
 * 🔄 架构设计：基于ResizeObserver的响应式设计
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import type { 
  ResponsiveConfig, 
  ComputedDimensions, 
  ViewportConfig 
} from '@/core/matrix/MatrixTypes';
import { 
  DEFAULT_RESPONSIVE_CONFIG, 
  MATRIX_SIZE 
} from '@/core/matrix/MatrixTypes';

// ===== Hook接口 =====

export interface UseResponsiveMatrixOptions {
  /** 响应式配置 */
  config?: Partial<ResponsiveConfig>;
  /** 容器引用 */
  containerRef: React.RefObject<HTMLElement>;
  /** 尺寸变化回调 */
  onDimensionsChange?: (dimensions: ComputedDimensions) => void;
}

export interface UseResponsiveMatrixReturn {
  /** 计算后的尺寸信息 */
  dimensions: ComputedDimensions;
  /** 是否正在计算 */
  isCalculating: boolean;
  /** 更新视口配置 */
  updateViewport: (viewport: Partial<ViewportConfig>) => void;
  /** 重新计算尺寸 */
  recalculate: () => void;
  /** 获取单元格样式 */
  getCellStyle: (x: number, y: number) => React.CSSProperties;
  /** 获取容器样式 */
  getContainerStyle: () => React.CSSProperties;
}

// ===== 工具函数 =====

/**
 * 计算矩阵尺寸
 */
function calculateDimensions(
  containerWidth: number,
  containerHeight: number,
  config: ResponsiveConfig
): ComputedDimensions {
  // 保持正方形比例
  const availableSize = config.maintainAspectRatio 
    ? Math.min(containerWidth, containerHeight)
    : Math.min(containerWidth, containerHeight);

  // 计算单元格尺寸
  const rawCellSize = availableSize / MATRIX_SIZE;
  const cellSize = Math.max(
    config.minCellSize,
    Math.min(config.maxCellSize, rawCellSize)
  );

  // 计算间距
  const cellGap = cellSize * config.gapRatio;
  
  // 计算实际矩阵尺寸
  const matrixWidth = MATRIX_SIZE * cellSize + (MATRIX_SIZE - 1) * cellGap;
  const matrixHeight = matrixWidth; // 保持正方形

  // 判断是否需要视口模式
  const needsViewport = matrixWidth > availableSize || matrixHeight > availableSize;

  // 计算可见范围
  let visibleRange = {
    startX: 0,
    endX: MATRIX_SIZE - 1,
    startY: 0,
    endY: MATRIX_SIZE - 1,
  };

  if (needsViewport && !config.viewport.showFullMatrix) {
    const visibleCells = Math.floor(availableSize / (cellSize + cellGap));
    const halfVisible = Math.floor(visibleCells / 2);
    
    visibleRange = {
      startX: Math.max(0, config.viewport.centerX - halfVisible),
      endX: Math.min(MATRIX_SIZE - 1, config.viewport.centerX + halfVisible),
      startY: Math.max(0, config.viewport.centerY - halfVisible),
      endY: Math.min(MATRIX_SIZE - 1, config.viewport.centerY + halfVisible),
    };
  }

  return {
    containerWidth,
    containerHeight,
    cellSize,
    cellGap,
    matrixWidth,
    matrixHeight,
    needsViewport,
    visibleRange,
  };
}

// ===== 主Hook =====

export function useResponsiveMatrix({
  config: userConfig = {},
  containerRef,
  onDimensionsChange,
}: UseResponsiveMatrixOptions): UseResponsiveMatrixReturn {
  // 合并配置
  const config = { ...DEFAULT_RESPONSIVE_CONFIG, ...userConfig };
  
  // 状态
  const [dimensions, setDimensions] = useState<ComputedDimensions>(() =>
    calculateDimensions(800, 600, config)
  );
  const [isCalculating, setIsCalculating] = useState(false);
  
  // ResizeObserver引用
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  // 计算尺寸
  const recalculate = useCallback(() => {
    if (!containerRef.current || !config.enabled) return;

    setIsCalculating(true);
    
    const rect = containerRef.current.getBoundingClientRect();
    const newDimensions = calculateDimensions(rect.width, rect.height, config);
    
    setDimensions(newDimensions);
    onDimensionsChange?.(newDimensions);
    
    setIsCalculating(false);
  }, [config, containerRef, onDimensionsChange]);

  // 更新视口
  const updateViewport = useCallback((viewport: Partial<ViewportConfig>) => {
    const newConfig = {
      ...config,
      viewport: { ...config.viewport, ...viewport }
    };
    
    if (!containerRef.current) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const newDimensions = calculateDimensions(rect.width, rect.height, newConfig);
    setDimensions(newDimensions);
  }, [config, containerRef]);

  // 获取单元格样式
  const getCellStyle = useCallback((x: number, y: number): React.CSSProperties => {
    const { cellSize, cellGap, visibleRange } = dimensions;
    
    // 检查是否在可见范围内
    if (x < visibleRange.startX || x > visibleRange.endX ||
        y < visibleRange.startY || y > visibleRange.endY) {
      return { display: 'none' };
    }

    // 计算相对位置（相对于可见区域）
    const relativeX = x - visibleRange.startX;
    const relativeY = y - visibleRange.startY;
    
    return {
      position: 'absolute',
      left: `${relativeX * (cellSize + cellGap)}px`,
      top: `${relativeY * (cellSize + cellGap)}px`,
      width: `${cellSize}px`,
      height: `${cellSize}px`,
      fontSize: `${Math.max(8, cellSize * 0.4)}px`,
    };
  }, [dimensions]);

  // 获取容器样式
  const getContainerStyle = useCallback((): React.CSSProperties => {
    const { matrixWidth, matrixHeight, needsViewport, containerWidth, containerHeight } = dimensions;
    
    if (needsViewport && !config.viewport.showFullMatrix) {
      // 视口模式：容器大小等于可用空间
      return {
        position: 'relative',
        width: `${Math.min(matrixWidth, containerWidth)}px`,
        height: `${Math.min(matrixHeight, containerHeight)}px`,
        overflow: 'hidden',
      };
    } else {
      // 完整显示模式
      return {
        position: 'relative',
        width: `${matrixWidth}px`,
        height: `${matrixHeight}px`,
      };
    }
  }, [dimensions, config.viewport.showFullMatrix]);

  // 设置ResizeObserver
  useEffect(() => {
    if (!containerRef.current || !config.enabled) return;

    resizeObserverRef.current = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        const newDimensions = calculateDimensions(width, height, config);
        setDimensions(newDimensions);
        onDimensionsChange?.(newDimensions);
      }
    });

    resizeObserverRef.current.observe(containerRef.current);

    return () => {
      resizeObserverRef.current?.disconnect();
    };
  }, [config, containerRef, onDimensionsChange]);

  // 初始计算
  useEffect(() => {
    recalculate();
  }, [recalculate]);

  return {
    dimensions,
    isCalculating,
    updateViewport,
    recalculate,
    getCellStyle,
    getContainerStyle,
  };
}
