/**
 * 响应式矩阵测试脚本
 * 测试矩阵在不同屏幕尺寸下的表现
 */

console.log('🎯 测试响应式矩阵功能\n');

// 模拟不同的容器尺寸
const testSizes = [
  { name: '桌面端大屏', width: 1200, height: 800 },
  { name: '桌面端中屏', width: 1024, height: 768 },
  { name: '平板横屏', width: 768, height: 1024 },
  { name: '平板竖屏', width: 1024, height: 768 },
  { name: '手机横屏', width: 667, height: 375 },
  { name: '手机竖屏', width: 375, height: 667 },
  { name: '小屏手机', width: 320, height: 568 },
];

// 矩阵配置
const MATRIX_SIZE = 33;
const MIN_CELL_SIZE = 8;
const MAX_CELL_SIZE = 32;
const GAP_RATIO = 0.1;

function calculateResponsiveSizes(containerWidth: number, containerHeight: number) {
  // 保持正方形比例
  const availableSize = Math.min(containerWidth, containerHeight);
  
  // 计算单元格尺寸
  const rawCellSize = availableSize / MATRIX_SIZE;
  const cellSize = Math.max(MIN_CELL_SIZE, Math.min(MAX_CELL_SIZE, rawCellSize));
  
  // 计算间距
  const gap = cellSize * GAP_RATIO;
  
  // 计算实际矩阵尺寸
  const matrixSize = MATRIX_SIZE * cellSize + (MATRIX_SIZE - 1) * gap;
  
  // 判断是否需要视口模式
  const needsViewport = matrixSize > availableSize;
  
  return {
    cellSize: Math.round(cellSize * 100) / 100,
    gap: Math.round(gap * 100) / 100,
    matrixSize: Math.round(matrixSize * 100) / 100,
    needsViewport,
    scaleFactor: cellSize / 20, // 相对于原始20px的缩放比例
  };
}

console.log('📊 不同屏幕尺寸下的矩阵表现：\n');

testSizes.forEach(({ name, width, height }) => {
  const result = calculateResponsiveSizes(width, height);
  
  console.log(`${name} (${width}x${height}):`);
  console.log(`  单元格尺寸: ${result.cellSize}px`);
  console.log(`  间距: ${result.gap}px`);
  console.log(`  矩阵总尺寸: ${result.matrixSize}px`);
  console.log(`  缩放比例: ${(result.scaleFactor * 100).toFixed(1)}%`);
  console.log(`  需要视口模式: ${result.needsViewport ? '是' : '否'}`);
  console.log('');
});

// 测试边界情况
console.log('🔍 边界情况测试：\n');

const edgeCases = [
  { name: '极小容器', width: 100, height: 100 },
  { name: '极窄容器', width: 200, height: 800 },
  { name: '极宽容器', width: 800, height: 200 },
  { name: '正方形小容器', width: 300, height: 300 },
  { name: '正方形大容器', width: 1000, height: 1000 },
];

edgeCases.forEach(({ name, width, height }) => {
  const result = calculateResponsiveSizes(width, height);
  
  console.log(`${name} (${width}x${height}):`);
  console.log(`  单元格尺寸: ${result.cellSize}px (${result.cellSize === MIN_CELL_SIZE ? '最小值' : result.cellSize === MAX_CELL_SIZE ? '最大值' : '正常'})`);
  console.log(`  矩阵适配: ${result.matrixSize <= Math.min(width, height) ? '完全适配' : '需要滚动'}`);
  console.log('');
});

// 计算不同设备的推荐配置
console.log('📱 设备推荐配置：\n');

const deviceRecommendations = [
  {
    device: '桌面端',
    minSize: { width: 1024, height: 768 },
    recommendation: '使用完整矩阵显示，单元格尺寸20-32px'
  },
  {
    device: '平板端',
    minSize: { width: 768, height: 1024 },
    recommendation: '使用完整矩阵显示，单元格尺寸15-25px'
  },
  {
    device: '手机端',
    minSize: { width: 375, height: 667 },
    recommendation: '使用视口模式，显示局部区域，单元格尺寸8-15px'
  }
];

deviceRecommendations.forEach(({ device, minSize, recommendation }) => {
  const result = calculateResponsiveSizes(minSize.width, minSize.height);
  console.log(`${device}:`);
  console.log(`  最小尺寸: ${minSize.width}x${minSize.height}`);
  console.log(`  计算结果: 单元格${result.cellSize}px, 矩阵${result.matrixSize}px`);
  console.log(`  推荐配置: ${recommendation}`);
  console.log('');
});

console.log('✅ 响应式矩阵测试完成！');
