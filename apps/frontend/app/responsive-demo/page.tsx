'use client';

import { Matrix } from '@/components/Matrix';
import { useState } from 'react';

export default function ResponsiveDemo() {
  const [containerSize, setContainerSize] = useState({ width: 600, height: 600 });

  const presetSizes = [
    { name: '桌面端', width: 800, height: 600 },
    { name: '平板端', width: 600, height: 800 },
    { name: '手机端', width: 375, height: 667 },
    { name: '小屏手机', width: 320, height: 568 },
    { name: '正方形', width: 500, height: 500 },
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">响应式矩阵演示</h1>
        
        {/* 控制面板 */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">容器尺寸控制</h2>
          
          {/* 预设尺寸 */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">预设尺寸:</label>
            <div className="flex flex-wrap gap-2">
              {presetSizes.map((preset) => (
                <button
                  key={preset.name}
                  onClick={() => setContainerSize({ width: preset.width, height: preset.height })}
                  className="px-3 py-1 bg-blue-100 hover:bg-blue-200 rounded text-sm"
                >
                  {preset.name} ({preset.width}×{preset.height})
                </button>
              ))}
            </div>
          </div>

          {/* 自定义尺寸 */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">宽度: {containerSize.width}px</label>
              <input
                type="range"
                min="200"
                max="1200"
                value={containerSize.width}
                onChange={(e) => setContainerSize(prev => ({ ...prev, width: parseInt(e.target.value) }))}
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">高度: {containerSize.height}px</label>
              <input
                type="range"
                min="200"
                max="1200"
                value={containerSize.height}
                onChange={(e) => setContainerSize(prev => ({ ...prev, height: parseInt(e.target.value) }))}
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* 矩阵演示区域 */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold mb-4">矩阵显示效果</h2>
          
          <div className="flex justify-center">
            <div 
              className="border-2 border-dashed border-gray-300 bg-gray-50 flex items-center justify-center"
              style={{ 
                width: `${containerSize.width}px`, 
                height: `${containerSize.height}px`,
                minWidth: '200px',
                minHeight: '200px'
              }}
            >
              <Matrix
                responsiveConfig={{
                  enabled: true,
                  minCellSize: 6,
                  maxCellSize: 24,
                  maintainAspectRatio: true,
                }}
                className="responsive"
              />
            </div>
          </div>

          {/* 信息显示 */}
          <div className="mt-4 p-4 bg-gray-50 rounded">
            <h3 className="font-medium mb-2">当前状态:</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>容器尺寸:</strong> {containerSize.width} × {containerSize.height} px
              </div>
              <div>
                <strong>宽高比:</strong> {(containerSize.width / containerSize.height).toFixed(2)}
              </div>
              <div>
                <strong>可用空间:</strong> {Math.min(containerSize.width, containerSize.height)} px
              </div>
              <div>
                <strong>预计单元格尺寸:</strong> {Math.max(6, Math.min(24, Math.min(containerSize.width, containerSize.height) / 33)).toFixed(1)} px
              </div>
            </div>
          </div>
        </div>

        {/* 说明文档 */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mt-6">
          <h2 className="text-xl font-semibold mb-4">响应式特性说明</h2>
          
          <div className="space-y-4 text-sm">
            <div>
              <h3 className="font-medium text-green-600">✅ 已实现的功能:</h3>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>矩阵保持正方形比例</li>
                <li>根据容器大小自动缩放单元格</li>
                <li>设置最小和最大单元格尺寸限制</li>
                <li>动态计算字体大小</li>
                <li>移动端CSS优化</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-blue-600">🔄 技术实现:</h3>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>使用ResizeObserver监听容器尺寸变化</li>
                <li>动态计算单元格尺寸和间距</li>
                <li>CSS aspect-ratio保持正方形比例</li>
                <li>响应式CSS媒体查询优化</li>
              </ul>
            </div>

            <div>
              <h3 className="font-medium text-orange-600">🚀 后续扩展:</h3>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>视口模式：小屏幕显示局部区域</li>
                <li>触摸手势：平移和缩放支持</li>
                <li>虚拟滚动：性能优化</li>
                <li>自适应内容：根据尺寸调整显示模式</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
